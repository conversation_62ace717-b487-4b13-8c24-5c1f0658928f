package com.illumio.data.filter;

import authservice.GetServiceAccountPermissionsRequest;
import authservice.GetServiceAccountPermissionsResponse;
import authservice.GetUserSessionPermissionsRequest;
import authservice.GetUserSessionPermissionsResponse;
import authservice.ReactorAuthServiceGrpc.ReactorAuthServiceStub;
import com.github.benmanes.caffeine.cache.Cache;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

@Slf4j
@Component
public class PermissionFilter extends AbstractGatewayFilterFactory<PermissionFilter.Config> {

    private final Cache<String, GetUserSessionPermissionsResponse> csiPermissionCache;
    private final ReactorAuthServiceStub authServiceStub;
    private final Cache<String, GetServiceAccountPermissionsResponse> serviceAccountCache;
    private final Counter permissionTotalCounter;
    private final Counter permissionFailedCounter;

    @Value("${grpctimeout}")
    Duration grpcTimeout;

    public static class Config {
        // Specify Filter config when needed
    }

    @Autowired
    public PermissionFilter(
            Cache<String, GetUserSessionPermissionsResponse> csiPermissionCache,
            ReactorAuthServiceStub authServiceStub,
            Cache<String, GetServiceAccountPermissionsResponse> serviceAccountCache,
            MeterRegistry meterRegistry
    ) {
        super(Config.class);
        this.csiPermissionCache = csiPermissionCache;
        this.authServiceStub = authServiceStub;
        this.serviceAccountCache = serviceAccountCache;
        this.permissionTotalCounter = meterRegistry.counter("gateway.permission.total.counter");
        this.permissionFailedCounter = meterRegistry.counter("gateway.permission.failed.counter");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {

            log.info(">> PermissionFilter invoked. Path = {}", exchange.getRequest().getPath());
            log.info("Inside permission filter, number of available processors: {}", Runtime.getRuntime().availableProcessors());
            String apikey = exchange.getRequest().getHeaders().getFirst("X-api-key");
            String apiSecret = exchange.getRequest().getHeaders().getFirst("X-api-secret");
            if ((apikey != null && !apikey.isEmpty()) && (apiSecret != null && !apiSecret.isEmpty())) {

                log.info("Service account request invoked, API Key = {} and Secret = {}", apikey, apiSecret);
                GetServiceAccountPermissionsResponse cachedresponse = serviceAccountCache.getIfPresent(apikey);
                if (cachedresponse != null) {
                        log.info("Cache hit for API key: {}", apikey);
                        exchange.getAttributes().put("serviceAccountSession", cachedresponse);
                        return chain.filter(exchange);
                }
                GetServiceAccountPermissionsRequest request = GetServiceAccountPermissionsRequest.newBuilder()
                        .setKey(apikey)
                        .setSecret(apiSecret)
                        .build();
                return authServiceStub.getServiceAccountPermissions(request)
                        .timeout(grpcTimeout)
                        .flatMap(serviceAccountResponse -> {

                            log.info("Received gRPC response for API token: {}", apikey);

                            // Cache the full gRPC response
                             serviceAccountCache.put(apikey, serviceAccountResponse);

                            // Store in request context for downstream filters/handlers
                            exchange.getAttributes().put("serviceAccountSession", serviceAccountResponse);

                            return chain.filter(exchange);
                        })
                        .onErrorResume(e -> {
                            log.error("gRPC error in PermissionFilter", e);
                            exchange.getResponse().setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
                            return exchange.getResponse().setComplete();
                        });
            }

            String csiToken = exchange.getRequest().getHeaders().getFirst("X-csi");
            if (exchange.getRequest().getMethod() == HttpMethod.OPTIONS) {
                log.info("Skipping filter for OPTIONS");
                return chain.filter(exchange);
            }

            if (csiToken == null || csiToken.isEmpty()) {
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                return exchange.getResponse().setComplete();
            }

            log.info(">> csiToken: {}", csiToken);

            // Check if permissions already present in the attribute
            GetUserSessionPermissionsResponse existingSession = exchange.getAttribute("grpcSession");
            if (existingSession != null) {
                log.info("grpcSession already present in exchange. Skipping PermissionFilter logic.");
                return chain.filter(exchange);
            }

            String xff = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");

            GetUserSessionPermissionsRequest request = GetUserSessionPermissionsRequest.newBuilder()
                    .setCsi(csiToken)
                    .setXff(xff)
                    .setReferrer("PermissionFilter")
                    .build();

            GetUserSessionPermissionsResponse cachedUserSession = csiPermissionCache.getIfPresent(csiToken);
            if (cachedUserSession != null) {
                log.info("Cache hit for CSI token: {}", csiToken);
                exchange.getAttributes().put("grpcSession", cachedUserSession);
                return chain.filter(exchange);
            }

            permissionTotalCounter.increment();
            return Mono.defer(() ->authServiceStub.getUserSessionPermissions(request)
                    .timeout(grpcTimeout)
                    .flatMap(grpcResponse -> {
                        if (grpcResponse.getCore().hasAccessRestriction()){
                            //terminate the request here
                            exchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
                            return exchange.getResponse().setComplete();
                        }

                        log.info("Received gRPC response for CSI token: {}", csiToken);

                        // Cache the full gRPC response
                        csiPermissionCache.put(csiToken, grpcResponse);

                        // Store in request context for downstream filters/handlers
                        exchange.getAttributes().put("grpcSession", grpcResponse);

                        return chain.filter(exchange);
                    })
                    .onErrorResume(e -> {
                        log.error("Census error ", e);
                        permissionFailedCounter.increment();
                        if (e instanceof StatusRuntimeException grpcEx) {
                            Status.Code code = grpcEx.getStatus().getCode();
                            String message = grpcEx.getStatus().getDescription();

                            if (code == Status.Code.NOT_FOUND) {
                                log.warn("Session not found or expired: {}", message);
                                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED); // or FORBIDDEN
                                byte[] body = "Session expired or does not exist.".getBytes(StandardCharsets.UTF_8);
                                exchange.getResponse().getHeaders().setContentType(MediaType.TEXT_PLAIN);
                                exchange.getResponse().getHeaders().setContentLength(body.length);
                                return exchange.getResponse()
                                        .writeWith(Mono.just(exchange.getResponse().bufferFactory().wrap(body)));
                            }
                        }
                        exchange.getResponse().setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
                        byte[] bytes = "Census is currently unavailable.".getBytes(StandardCharsets.UTF_8);
                        exchange.getResponse().getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE);
                        exchange.getResponse().getHeaders().setContentLength(bytes.length);

                        DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
                        return exchange.getResponse().writeWith(Mono.just(buffer));
                    }));
        };
    }
}