package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaReceiverConfiguration {
    private final LabelRecommendationConfig labelRecommendationConfig;

    @Bean
    public KafkaReceiver<String, String> kafkaReceiver() {
        // Use the utility to create consumer properties
        Map<String, Object> consumerProps = com.illumio.data.util.KafkaConsumerPropsUtil.createConsumerProps(labelRecommendationConfig);

        ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                        .subscription(
                                Collections.singleton(
                                        labelRecommendationConfig.getKafkaReceiverConfig().getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }
}
