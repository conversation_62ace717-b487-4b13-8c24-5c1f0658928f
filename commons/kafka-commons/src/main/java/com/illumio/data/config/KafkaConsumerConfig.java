package com.illumio.data.config;

/**
 * Common interface for Kafka common configuration properties.
 */
public interface KafkaConsumerConfig {

    /**
     * Common configuration interface for Kafka connection settings.
     */
    interface CommonConfig {

        String getBootstrapServers();

        Boolean getIsSasl();

        String getSaslJaasConfig();

        Integer getMetadataMaxAgeMs();

        Integer getConnectionMaxIdleMs();
    }

    /**
     * Consumer-specific configuration interface.
     */
    interface ReceiverConfig {

        String getTopic();

        String getGroupId();

        String getGroupInstanceId();

        String getAutoOffsetReset();

        Integer getRequestTimeoutMs();

        Integer getMaxPollRecords();

        Integer getMaxPartitionFetchBytes();

        String getAssignmentStrategy();

        Integer getMaxPollIntervalMs();

        Integer getSessionTimeoutMs();

        Integer getHeartbeatIntervalMs();
    }

    /**
     * Get the common configuration.
     */
    CommonConfig getKafkaCommonConfig();

    /**
     * Get the receiver configuration.
     */
    ReceiverConfig getKafkaReceiverConfig();
}
