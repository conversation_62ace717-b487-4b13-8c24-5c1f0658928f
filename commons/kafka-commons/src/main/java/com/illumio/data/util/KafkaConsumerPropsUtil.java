package com.illumio.data.util;

import com.illumio.data.config.KafkaConsumerConfig;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.HashMap;
import java.util.Map;

/**
 * Utility class for creating Kafka consumer properties from common configuration interfaces.
 */
public class KafkaConsumerPropsUtil {

    /**
     * Creates a map of consumer properties from the provided configuration.
     *
     * @param config the configuration implementing KafkaConsumerConfig
     * @return Map of consumer properties
     */
    public static Map<String, Object> createConsumerProps(KafkaConsumerConfig config) {
        Map<String, Object> consumerProps = new HashMap<>();
        
        KafkaConsumerConfig.CommonConfig commonConfig = config.getKafkaCommonConfig();
        KafkaConsumerConfig.ReceiverConfig receiverConfig = config.getKafkaReceiverConfig();
        
        // Basic consumer configuration
        consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                commonConfig.getBootstrapServers());
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, receiverConfig.getGroupId());
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        // Optional group instance ID
        if (receiverConfig.getGroupInstanceId() != null) {
            consumerProps.put(ConsumerConfig.GROUP_INSTANCE_ID_CONFIG,
                    receiverConfig.getGroupInstanceId());
        }

        // Consumer behavior configuration
        if (receiverConfig.getRequestTimeoutMs() != null) {
            consumerProps.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG,
                    receiverConfig.getRequestTimeoutMs());
        }
        if (receiverConfig.getAutoOffsetReset() != null) {
            consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                    receiverConfig.getAutoOffsetReset());
        }
        if (receiverConfig.getMaxPollRecords() != null) {
            consumerProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                    receiverConfig.getMaxPollRecords());
        }
        if (receiverConfig.getMaxPartitionFetchBytes() != null) {
            consumerProps.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                    receiverConfig.getMaxPartitionFetchBytes());
        }
        if (receiverConfig.getAssignmentStrategy() != null) {
            consumerProps.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
                    receiverConfig.getAssignmentStrategy());
        }
        if (receiverConfig.getMaxPollIntervalMs() != null) {
            consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG,
                    receiverConfig.getMaxPollIntervalMs());
        }
        if (receiverConfig.getSessionTimeoutMs() != null) {
            consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
                    receiverConfig.getSessionTimeoutMs());
        }
        if (receiverConfig.getHeartbeatIntervalMs() != null) {
            consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,
                    receiverConfig.getHeartbeatIntervalMs());
        }

        // Common client configuration
        if (commonConfig.getMetadataMaxAgeMs() != null) {
            consumerProps.put(CommonClientConfigs.METADATA_MAX_AGE_CONFIG,
                    commonConfig.getMetadataMaxAgeMs());
        }
        if (commonConfig.getConnectionMaxIdleMs() != null) {
            consumerProps.put(CommonClientConfigs.CONNECTIONS_MAX_IDLE_MS_CONFIG,
                    commonConfig.getConnectionMaxIdleMs());
        }
        
        // SASL configuration
        if (commonConfig.getIsSasl() != null && commonConfig.getIsSasl()) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(SaslConfigs.SASL_JAAS_CONFIG, commonConfig.getSaslJaasConfig());
        }

        return consumerProps;
    }

    /**
     * Creates consumer properties with additional custom properties.
     *
     * @param config the configuration implementing KafkaConsumerConfig
     * @param additionalProps additional properties to add/override
     * @return Map of consumer properties
     */
    public static Map<String, Object> createConsumerProps(KafkaConsumerConfig config,
            Map<String, Object> additionalProps) {
        Map<String, Object> consumerProps = createConsumerProps(config);
        if (additionalProps != null) {
            consumerProps.putAll(additionalProps);
        }
        return consumerProps;
    }
}
