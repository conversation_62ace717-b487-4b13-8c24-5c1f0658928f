apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "FlowDecorator.fullname" . }}-env-configmap
  labels:
    {{- include "FlowDecorator.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: INFO
    
    spring:
      application:
        name: flow-decorator
      output:
        ansi:
          enabled: ALWAYS


    kafkaReceiverConfig:
      bootstrapServers: "{{.Values.kafkaCommonConfig.bootstrapServers}}"
      isSasl: {{.Values.kafkaCommonConfig.isSasl}}
      topic: "{{.Values.kafkaReceiverConfig.topic}}"
      groupId: "{{.Values.kafkaReceiverConfig.groupId}}"
      autoOffsetReset: "{{.Values.kafkaReceiverConfig.autoOffsetReset}}"
      requestTimeoutMs: "{{.Values.kafkaReceiverConfig.requestTimeoutMs}}"
      maxPollRecords: "{{.Values.kafkaReceiverConfig.maxPollRecords}}"
      maxPartitionFetchBytes: "{{.Values.kafkaReceiverConfig.maxPartitionFetchBytes}}"
    kafkaSenderConfig:
      bootstrapServers: "{{.Values.kafkaCommonConfig.bootstrapServers}}"
      isSasl: {{.Values.kafkaCommonConfig.isSasl}}
      topic: "{{.Values.kafkaSenderConfig.topic}}"
    inventoryConfig:
      host: "{{.Values.inventoryConfig.host}}"
      port: {{.Values.inventoryConfig.port}}
      useTls: {{.Values.inventoryConfig.useTls}}
    resilienceConfig:
      failureRateThreshold: "{{.Values.resilienceConfig.failureRateThreshold}}"
      waitDurationInOpenState: "{{.Values.resilienceConfig.waitDurationInOpenState}}"
      slidingWindowSize: "{{.Values.resilienceConfig.slidingWindowSize}}"
      maxAttempts: "{{.Values.resilienceConfig.maxAttempts}}"
      waitDuration: "{{.Values.resilienceConfig.waitDuration}}"
      timeoutDuration: "{{.Values.resilienceConfig.timeoutDuration}}"