# Default values for sink connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: DEBUG
    
ports:
- name: admin
  port: 8084
- name: rest
  port: 8081

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: flow-decorator
  tag:      # value given at helm deployment 
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 4
  targetCPUUtilizationPercentage: 60
  targetMemoryUtilizationPercentage: 60

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

extraLabels: {}

ansi:
  enabled: ALWAYS
webApplicationType: none
kafkaCommonConfig:
  bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
  isSasl: true
kafkaReceiverConfig:
  bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
  topic: aggregated-flow-v1
  groupId: flow-decorator-group
  autoOffsetReset: latest
  requestTimeoutMs: 180000
  maxPollRecords: 500
  maxPartitionFetchBytes: "1048576"
kafkaSenderConfig:
  bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
  topic: decorated-flow-v1
inventoryConfig:
  host: inventory.data.azure.westus3.dev.cloud.ilabs.io
  port: 8080
  useTls: false
  cert: ""
resilienceConfig:
  failureRateThreshold: 50
  waitDurationInOpenState: "10s"
  slidingWindowSize: 10
  maxAttempts: 3
  waitDuration: "1s"
  timeoutDuration: "5s"

eventhub:
  password:                 # should give at deployment time

