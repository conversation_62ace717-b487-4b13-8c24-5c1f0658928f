package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.*;

@Configuration
@RequiredArgsConstructor
public class KafkaReceiverConfiguration {
    private final IpClassificationMmdbConfig ipClassificationMmdbConfig;

    @Bean
    public KafkaReceiver<String, String> kafkaReceiver() {
        Map<String, Object> consumerProps = createConsumerProps();
        ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                        .subscription(Collections.singleton(
                                ipClassificationMmdbConfig.getKafkaReceiverConfig().getTopic()));
        return KafkaReceiver.create(receiverOptions);
    }

    private Map<String, Object> createConsumerProps() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                ipClassificationMmdbConfig.getKafkaCommonConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getGroupId());
        if (null != ipClassificationMmdbConfig.getKafkaReceiverConfig().getGroupInstanceId()) {
            consumerProps.put(ConsumerConfig.GROUP_INSTANCE_ID_CONFIG,
                    ipClassificationMmdbConfig.getKafkaReceiverConfig().getGroupInstanceId());
        }
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(
                ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getRequestTimeoutMs());
        consumerProps.put(
                ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getAutoOffsetReset());
        if (null != ipClassificationMmdbConfig.getKafkaCommonConfig().getIsSasl()
                && ipClassificationMmdbConfig.getKafkaCommonConfig().getIsSasl()) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    ipClassificationMmdbConfig.getKafkaCommonConfig().getSaslJaasConfig());
        }
        consumerProps.put(
                ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getMaxPollRecords());
        consumerProps.put(
                ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getMaxPartitionFetchBytes());
        consumerProps.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getAssignmentStrategy());
        // Maximum time between poll() calls before a rebalance is triggered
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getMaxPollIntervalMs());
        // Maximum time the coordinator waits for a heartbeat before marking the consumer as dead
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getSessionTimeoutMs());
        // Frequency of heartbeats to keep the consumer's session active
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,
                ipClassificationMmdbConfig.getKafkaReceiverConfig().getHeartbeatIntervalMs());
        consumerProps.put(CommonClientConfigs.METADATA_MAX_AGE_CONFIG,
                ipClassificationMmdbConfig.getKafkaCommonConfig().getMetadataMaxAgeMs());
        consumerProps.put(CommonClientConfigs.CONNECTIONS_MAX_IDLE_MS_CONFIG,
                ipClassificationMmdbConfig.getKafkaCommonConfig().getConnectionMaxIdleMs());
        return consumerProps;
    }

}
