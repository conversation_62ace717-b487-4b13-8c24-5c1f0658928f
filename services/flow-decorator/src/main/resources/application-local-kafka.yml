logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
    com.illumio.data.components: DEBUG
    io.github.resilience4j: DEBUG
    io.github.resilience4j.circuitbreaker: DEBUG
spring:
  application:
    name: flow-decorator
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    export:
      prometheus:
        enabled: true
  server:
    port: 9464

server:
  port : 8089
kafkaReceiverConfig:
  bootstrapServers: localhost:19092
  isSasl: false
  saslJaasConfig: _DO_NOT_COMMIT_
  topic: aggregated-flow-v1
  groupId: flow-decorator-group
  autoOffsetReset: latest
  requestTimeoutMs: 180000
  maxPollRecords: 20000
  maxPartitionFetchBytes: 5242880
kafkaSenderConfig:
  bootstrapServers: localhost:19092
  isSasl: false
  saslJaasConfig: _DO_NOT_COMMIT_
  topic: decorated-flow-v1
inventoryConfig:
  host: localhost
  port: 7860
  cert: ""
  use-tls: true
resilienceConfig:
  failureRateThreshold: 50
  waitDurationInOpenState: 10s
  slidingWindowSize: 10
  maxAttempts: 3
  waitDuration: 1s
  timeoutDuration: "5s"